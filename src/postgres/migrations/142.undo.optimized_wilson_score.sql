-- Revert Wilson score function optimizations
-- Restore original numeric-based functions

DROP FUNCTION wilson_score_lower_bound(double precision, double precision, double precision);
DROP FUNCTION wilson_score_upper_bound(double precision, double precision, double precision);
DROP FUNCTION wilson_score_lower_bound_95(double precision, double precision);
DROP FUNCTION wilson_score_upper_bound_95(double precision, double precision);

-- Restore original Wilson score functions with numeric types
CREATE FUNCTION wilson_score_lower_bound(ns numeric, nf numeric, z numeric)
  RETURNS numeric
  AS $$
    SELECT 
      (ns + 0.5 * z * z) / (ns + nf + z * z) -
      z / (ns + nf + z * z) * SQRT(ns * nf / (ns + nf) + 0.25 * z * z)
  $$
  LANGUAGE SQL;

CREATE FUNCTION wilson_score_upper_bound(ns numeric, nf numeric, z numeric)
  RETURNS numeric
  AS $$
    SELECT 
      (ns + 0.5 * z * z) / (ns + nf + z * z) +
      z / (ns + nf + z * z) * SQRT(ns * nf / (ns + nf) + 0.25 * z * z)
  $$
  LANGUAGE SQL;
