DROP VIEW "graphql_user";
DROP VIEW "graphql_user_movie_view";
DROP VIEW "graphql_user_movie_mark";
DROP VIEW "graphql_user_movie_log";
DROP VIEW "graphql_user_movie_friend_mark";
DROP VIEW "graphql_watchlist";
DROP VIEW "graphql_feed";
DROP VIEW "graphql_genre";
DROP MATERIALIZED VIEW "graphql_movie_genre";
DROP VIEW "graphql_movie_director";
DROP MATERIALIZED VIEW "graphql_person";
DROP MATERIALIZED VIEW "graphql_person_image";
DROP MATERIALIZED VIEW "graphql_movie";
DROP MATERIALIZED VIEW "graphql_movie_image";
DROP MATERIALIZED VIEW "graphql_movie_top";
DROP MATERIALIZED VIEW "graphql_friend";
DROP FUNCTION bernoulli_parameter_confidence_interval_lower_bound;
DROP FUNCTION bernoulli_parameter_confidence_interval_upper_bound;
-- DROP FUNCTION wilson_score_lower_bound;
-- DROP FUNCTION wilson_score_upper_bound;

CREATE VIEW "graphql_user" AS (
  SELECT
    kinopoisk_user.id as "id",
    kinopoisk_user.name as "name",
    COALESCE(account.slug, kinopoisk_user.name) as "slug"
  FROM
    kinopoisk_user
  LEFT JOIN
    account
    ON account.kinopoisk_id = kinopoisk_user.id
);

CREATE VIEW "graphql_user_movie_view" AS (
  SELECT
    zyr_movie_mark_history.account_id as "user_id",
    zyr_movie_mark_history.movie_id as "movie_id",
    zyr_movie_mark_history."timestamp" as "timestamp"
  FROM
    zyr_movie_mark_history
  WHERE
    zyr_movie_mark_history.mark IS NULL
    AND EXISTS (
      SELECT
      FROM
        account
      WHERE
        account.id = zyr_movie_mark_history.account_id
    )

  UNION ALL

  SELECT
    kinopoisk_movie_mark.user_id as "user_id",
    kinopoisk_movie_mark.movie_id as "movie_id",
    kinopoisk_movie_mark.updated_at as "timestamp"
  FROM
    kinopoisk_movie_mark
  WHERE
    NOT EXISTS (
      SELECT
      FROM
        account
      WHERE
        account.kinopoisk_id = kinopoisk_movie_mark.user_id
    )
);

CREATE VIEW "graphql_user_movie_mark" AS (
  SELECT
    kinopoisk_movie_mark.user_id as "user_id",
    kinopoisk_movie_mark.movie_id as "movie_id",
    kinopoisk_movie_mark.mark as "mark",
    kinopoisk_movie_mark.updated_at as "timestamp"
  FROM
    kinopoisk_movie_mark
);

CREATE VIEW "graphql_user_movie_log" AS (
  SELECT
    zyr_movie_mark_history.account_id as "user_id",
    zyr_movie_mark_history.movie_id as "movie_id",
    zyr_movie_mark_history.mark as "mark",
    zyr_movie_mark_history."timestamp" as "timestamp"
  FROM
    zyr_movie_mark_history
);

CREATE MATERIALIZED VIEW "graphql_friend" AS (
  WITH kinopoisk_movie_mark_normalized AS (
    SELECT
      kinopoisk_movie_mark.user_id as user_id,
      kinopoisk_movie_mark.movie_id as movie_id,
      kinopoisk_movie_mark.created_at as created_at,
      CASE
        WHEN STDDEV_SAMP(mark) OVER user_marks_window > 0
        THEN ROUND(AVG(mark) OVER () + ((mark - AVG(mark) OVER user_marks_window) / STDDEV_SAMP(mark) OVER user_marks_window) * STDDEV_SAMP(mark) OVER ())
        ELSE mark
      END as mark
    FROM
      kinopoisk_movie_mark
    WINDOW
      user_marks_window AS (PARTITION BY kinopoisk_movie_mark.user_id)
  ), user_mark_mean_absolute_error AS (
    SELECT
      kinopoisk_movie_mark.user_id as user_id,
      another_kinopoisk_movie_mark.user_id as friend_id,
      SUM(
        ABS(
          (
            CASE
              WHEN kinopoisk_movie_mark.mark >= 9 THEN 4
              WHEN kinopoisk_movie_mark.mark >= 7 THEN 3
              WHEN kinopoisk_movie_mark.mark >= 5 THEN 2
              ELSE 1
            END
          )
          - (
            CASE
              WHEN another_kinopoisk_movie_mark.mark >= 9 THEN 4
              WHEN another_kinopoisk_movie_mark.mark >= 7 THEN 3
              WHEN another_kinopoisk_movie_mark.mark >= 5 THEN 2
              ELSE 1
            END
          ) + 0.0
        )
      ) / COUNT(*) as mae
    FROM
      account
    JOIN
      kinopoisk_movie_mark_normalized kinopoisk_movie_mark
      ON kinopoisk_movie_mark.user_id = account.kinopoisk_id
    JOIN
      kinopoisk_movie_mark_normalized another_kinopoisk_movie_mark
      ON another_kinopoisk_movie_mark.movie_id = kinopoisk_movie_mark.movie_id
      AND another_kinopoisk_movie_mark.user_id != kinopoisk_movie_mark.user_id
    WHERE
      (
        kinopoisk_movie_mark.mark >= 9
        OR another_kinopoisk_movie_mark.mark >= 9
      )
    GROUP BY
      kinopoisk_movie_mark.user_id,
      another_kinopoisk_movie_mark.user_id
    HAVING
      COUNT(*) >= 30
    ORDER BY
      mae ASC NULLS LAST
  ), ordered_friend AS (
    SELECT
      user_mark_mean_absolute_error.user_id as user_id,
      user_mark_mean_absolute_error.friend_id,
      ROW_NUMBER() OVER (PARTITION BY user_mark_mean_absolute_error.user_id ORDER BY user_mark_mean_absolute_error.mae ASC NULLS LAST) as "order"
    FROM
      user_mark_mean_absolute_error
  )

  SELECT
    ordered_friend.user_id as user_id,
    ordered_friend.friend_id as friend_id,
    ordered_friend."order" as "order"
  FROM
    ordered_friend
  WHERE
    ordered_friend."order" <= 200
);

CREATE VIEW "graphql_user_movie_friend_mark" AS (
  SELECT
    kinopoisk_user.id as user_id,
    kinopoisk_movie.id as movie_id,
    COUNT(NULLIF(kinopoisk_movie_mark.mark = 1, false)) as "1",
    COUNT(NULLIF(kinopoisk_movie_mark.mark = 2, false)) as "2",
    COUNT(NULLIF(kinopoisk_movie_mark.mark = 3, false)) as "3",
    COUNT(NULLIF(kinopoisk_movie_mark.mark = 4, false)) as "4",
    COUNT(NULLIF(kinopoisk_movie_mark.mark = 5, false)) as "5",
    COUNT(NULLIF(kinopoisk_movie_mark.mark = 6, false)) as "6",
    COUNT(NULLIF(kinopoisk_movie_mark.mark = 7, false)) as "7",
    COUNT(NULLIF(kinopoisk_movie_mark.mark = 8, false)) as "8",
    COUNT(NULLIF(kinopoisk_movie_mark.mark = 9, false)) as "9",
    COUNT(NULLIF(kinopoisk_movie_mark.mark = 10, false)) as "10"
  FROM
    kinopoisk_user
  CROSS JOIN
     kinopoisk_movie
  JOIN
    graphql_friend
    ON graphql_friend.user_id = kinopoisk_user.id
  JOIN
    kinopoisk_movie_mark
    ON kinopoisk_movie_mark.user_id = graphql_friend.friend_id
    AND kinopoisk_movie_mark.movie_id = kinopoisk_movie.id
  GROUP BY
    kinopoisk_user.id, kinopoisk_movie.id
);

CREATE VIEW "graphql_watchlist" AS (
  SELECT
    zyr_watchlist.account_id as "user_id",
    zyr_watchlist.movie_id as "movie_id",
    zyr_watchlist."created_at" as "timestamp"
  FROM
    zyr_watchlist
);

CREATE VIEW "graphql_feed" AS (
  SELECT
  kinopoisk_user.id as "user_id",
  friend_kinopoisk_user.id as "friend_id",
    kinopoisk_movie_mark.movie_id as "movie_id",
    kinopoisk_movie_mark.created_at as "date",
    kinopoisk_movie_mark.mark as "mark"
  FROM
    kinopoisk_user
  JOIN
    kinopoisk_friend
    ON kinopoisk_friend.user_id = kinopoisk_user.id
  JOIN
    kinopoisk_movie_mark
    ON kinopoisk_movie_mark.user_id = kinopoisk_friend.friend_id
  JOIN
    kinopoisk_user friend_kinopoisk_user
    ON friend_kinopoisk_user.id = kinopoisk_movie_mark.user_id
  ORDER BY
    kinopoisk_movie_mark.created_at DESC
);

CREATE VIEW "graphql_genre" AS
    SELECT * FROM (
        VALUES
            (1, 'drama', 'драма', 1),
            (2, 'comedy', 'комедия', 2),
            (3, 'romance', 'романтический', 3),
            (4, 'thriller', 'триллер', 4),
            (5, 'action', 'боевик', 5),
            (6, 'crime', 'криминал', 6),
            (7, 'horror', 'ужасы', 7),
            (8, 'adventure', 'приключения', 8),
            (9, 'animation', 'мультфильм', 9),
            (10, 'fantasy', 'фэнтези', 10),
            (11, 'mystery', 'мистика', 11),
            (12, 'science-fiction', 'фантастика', 12),
            (13, 'documentary', 'документальный', 13),
            (14, 'history', 'исторический', 14),
            (15, 'war', 'военный', 15),
            (16, 'music', 'музыкальный', 16),
            (17, 'western', 'вестерн', 17),
            (18, 'family', 'семейный', 18),
            (19, 'detective', 'детектив', 19)
    ) AS t (id, slug, label, "order");

CREATE MATERIALIZED VIEW "graphql_movie_genre" AS (
    WITH wikidata_movie_genre_synthetic AS (
        SELECT
          wikidata_movie_genre.movie_id as movie_id,
          wikidata_movie_genre.genre_id as genre_id
        FROM
          wikidata_movie_genre

        UNION ALL

        SELECT
          wikidata_movie.id as movie_id,
          wikidata_genre.id as genre_id
        FROM
          wikipedia_article_genre
        JOIN
          wikidata_movie
          ON (wikidata_movie.wikipedia_ru_slug = wikipedia_article_genre.article_slug AND wikipedia_article_genre.article_lang = 'ru')
          OR (wikidata_movie.wikipedia_en_slug = wikipedia_article_genre.article_slug AND wikipedia_article_genre.article_lang = 'en')
        JOIN
          wikidata_genre
          ON (wikidata_genre.wikipedia_ru_slug = wikipedia_article_genre.genre_slug AND wikipedia_article_genre.genre_lang = 'ru')
          OR (wikidata_genre.wikipedia_en_slug = wikipedia_article_genre.genre_slug AND wikipedia_article_genre.genre_lang = 'en')

        UNION ALL

        SELECT
          wikidata_movie.id as movie_id,
          wikidata_genre_hierarchy.parent_id as genre_id
        FROM
          wikipedia_article_genre
        JOIN
          wikidata_movie
          ON (wikidata_movie.wikipedia_ru_slug = wikipedia_article_genre.article_slug AND wikipedia_article_genre.article_lang = 'ru')
          OR (wikidata_movie.wikipedia_en_slug = wikipedia_article_genre.article_slug AND wikipedia_article_genre.article_lang = 'en')
        JOIN
          wikidata_genre
          ON (wikidata_genre.wikipedia_ru_slug = wikipedia_article_genre.genre_slug AND wikipedia_article_genre.genre_lang = 'ru')
          OR (wikidata_genre.wikipedia_en_slug = wikipedia_article_genre.genre_slug AND wikipedia_article_genre.genre_lang = 'en')
        JOIN
          wikidata_genre_hierarchy
          ON wikidata_genre_hierarchy.child_id = wikidata_genre.id
    )

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        1 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 18
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 130232
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        2 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 35
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 157443
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        3 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 10749
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 1054574
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        4 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 53
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 2484376
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        5 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 28
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 188473
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        6 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 80
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 959790
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        7 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 27
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 200092
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        8 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 12
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 319221
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        9 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 16
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 202866
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        10 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 14
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 132311
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        11 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 9648
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 1200678
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        12 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 878
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 471839
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        13 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 99
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 93204
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        14 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 36
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 17013749
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        15 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 10752
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 369747
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        16 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 10402
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 842256
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        17 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 37
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 172980
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        18 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
            SELECT
            FROM tmdb_movie_genre
            WHERE tmdb_movie_genre.movie_id = wikidata_movie.tmdb_id
            AND tmdb_movie_genre.genre_id = 10751
        )
        OR EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id = 1361932
        )

    UNION ALL

    SELECT
        wikidata_movie.kinopoisk_id AS "movie_id",
        19 AS "genre_id"
    FROM
        wikidata_movie
    WHERE
        EXISTS (
          SELECT
          FROM wikidata_movie_genre_synthetic
          WHERE wikidata_movie_genre_synthetic.movie_id = wikidata_movie.id
          AND wikidata_movie_genre_synthetic.genre_id IN (186424, 25533274)
        )
);

CREATE INDEX graphql_movie_genre_pk ON graphql_movie_genre (movie_id, genre_id);
CREATE INDEX graphql_movie_genre_movie_id ON graphql_movie_genre (movie_id);
CREATE INDEX graphql_movie_genre_genre_id ON graphql_movie_genre (genre_id);

CREATE VIEW "graphql_movie_director" AS (
  SELECT
    wikidata_movie.kinopoisk_id as "movie_id",
    wikidata_movie_crew_member.person_id as "person_id",
    wikidata_movie_crew_member."order" as "order"
  FROM
    wikidata_movie
  JOIN
    wikidata_movie_crew_member
    ON wikidata_movie_crew_member.movie_id = wikidata_movie.id
  JOIN
    wikidata_person
    ON wikidata_person.id = wikidata_movie_crew_member.person_id
  WHERE
    wikidata_person.full_name_ru IS NOT NULL
);

CREATE MATERIALIZED VIEW "graphql_person" AS (
  SELECT
    wikidata_person.id as "id",
    zyr_person.slug as "slug",
    wikidata_person.full_name_ru as "full_name",
    jsonb_build_object(
      'wikipediaEn', wikidata_person.wikipedia_en_slug,
      'wikipediaRu', wikidata_person.wikipedia_ru_slug
    ) as "links",
    to_tsvector('simple', TRANSLATE(wikidata_person.full_name_ru, 'Ёё ', 'Ее ') || ' ' || COALESCE(wikidata_person.full_name_en, '')) as "ts",
    TRANSLATE(wikidata_person.full_name_ru, 'Ёё ', 'Ее ') || ' ' || COALESCE(wikidata_person.full_name_en, '') as "ts_text"
  FROM
    wikidata_person
  LEFT JOIN
    zyr_person
    ON zyr_person.id = wikidata_person.id
  WHERE
    wikidata_person.full_name_ru IS NOT NULL
);

CREATE INDEX graphql_person_pk ON graphql_person (id);
CREATE INDEX graphql_person_slug ON graphql_person (slug);
CREATE INDEX graphql_person_ts_text ON graphql_person USING GIN (ts_text gin_trgm_ops);
CREATE INDEX graphql_person_ts ON graphql_person USING GIN (ts);

CREATE MATERIALIZED VIEW "graphql_person_image" AS (
  SELECT
    wikidata_person.id as "person_id",
    image.url as "url",
    tmdb_person_image.width as "width",
    tmdb_person_image.height as "height",
    tmdb_person_image.order as "order"
  FROM
    wikidata_person
  JOIN
    tmdb_person
    ON tmdb_person.id = wikidata_person.tmdb_id
  JOIN
    tmdb_person_image
    ON tmdb_person_image.person_id = tmdb_person.id
  JOIN
    image
    ON image.source_url = tmdb_person_image.url
);

CREATE INDEX graphql_person_image_person_id ON graphql_person_image (person_id);

CREATE MATERIALIZED VIEW "graphql_movie" AS (
  SELECT
    kinopoisk_movie.id as "id",
    zyr_movie.slug as "slug",
    kinopoisk_movie.title as "title",
    kinopoisk_movie.year as "year",
    CASE
      WHEN wikidata_movie.duration_mins IS NOT NULL
      THEN jsonb_build_object('minutes', wikidata_movie.duration_mins)
    END as "duration",
    jsonb_build_object(
      'kinopoisk', kinopoisk_movie.id,
      'wikipediaEn', wikidata_movie.wikipedia_en_slug,
      'wikipediaRu', wikidata_movie.wikipedia_ru_slug
    ) as "links",
    (
      SELECT
        llm_logline.logline
      FROM
        wikipedia_article
      JOIN
        llm_logline
        ON llm_logline.wikipedia_slug = wikipedia_article.slug
        AND llm_logline.wikipedia_lang = wikipedia_article.lang
      WHERE
        (
          wikipedia_article.slug = wikidata_movie.wikipedia_en_slug
          AND wikipedia_article.lang = 'en'
        ) OR (
          wikipedia_article.slug = wikidata_movie.wikipedia_ru_slug
          AND wikipedia_article.lang = 'ru'
        )
      ORDER BY
        llm_logline.created_at DESC
      LIMIT
        1
    ) as "logline",
    zyr_movie.subtitle as "subtitle",
    (to_tsvector('simple', TRANSLATE(kinopoisk_movie.title, 'Ёё ', 'Ее ') || ' ' || COALESCE(wikidata_movie.title_en, ''))) as "ts",
    TRANSLATE(kinopoisk_movie.title, 'Ёё ', 'Ее ') || ' ' || COALESCE(wikidata_movie.title_en, '') as "ts_text"
  FROM
    kinopoisk_movie
  LEFT JOIN
    zyr_movie
    ON zyr_movie.id = kinopoisk_movie.id
  LEFT JOIN
    wikidata_movie
    ON wikidata_movie.kinopoisk_id = kinopoisk_movie.id
);

CREATE INDEX graphql_movie_pk ON graphql_movie (id);
CREATE INDEX graphql_movie_slug ON graphql_movie (slug);
CREATE INDEX graphql_movie_ts_text ON graphql_movie USING GIN (ts_text gin_trgm_ops);
CREATE INDEX graphql_movie_ts ON graphql_movie USING GIN (ts);

CREATE MATERIALIZED VIEW "graphql_movie_image" AS (
  SELECT
    wikidata_movie.kinopoisk_id as "movie_id",
    image.url as "url",
    tmdb_movie_image.width as "width",
    tmdb_movie_image.height as "height",
    tmdb_movie_image."order" as "order"
  FROM
    wikidata_movie
  JOIN
    tmdb_movie
    ON tmdb_movie.id = wikidata_movie.tmdb_id
  JOIN
    tmdb_movie_image
    ON tmdb_movie_image.movie_id = tmdb_movie.id
  JOIN
    image
    ON image.source_url = tmdb_movie_image.url
);

CREATE INDEX graphql_movie_image_movie_id ON graphql_movie_image (movie_id);


CREATE FUNCTION wilson_score_lower_bound(positive numeric, negative numeric, confidence numeric)
  RETURNS numeric
  AS $$
    SELECT 
      
  $$
  LANGUAGE SQL;

CREATE FUNCTION wilson_score_upper_bound(positive numeric, negative numeric, confidence numeric)
  RETURNS numeric
  AS $$
    SELECT (
      (1.0 * positive) / (positive + negative) +
      1.0 * confidence * confidence / (2 * (positive + negative)) +
      confidence * SQRT(
        (
          (1.0 * positive) / (positive + negative) *
           (1 - (1.0 * positive) / (positive + negative)) +
          1.0 * confidence * confidence / (4 * (positive + negative))
        ) / (
          positive + negative
        )
      )
    ) / (
        1 + 1.0 * confidence * confidence / (positive + negative)
    )
  $$
  LANGUAGE SQL;

CREATE MATERIALIZED VIEW "graphql_movie_top" AS (
  WITH kinopoisk_movie_mark_normalized AS (
    SELECT
      kinopoisk_movie_mark.user_id as user_id,
      kinopoisk_movie_mark.movie_id as movie_id,
      kinopoisk_movie_mark.created_at as created_at,
      CASE
        WHEN STDDEV_SAMP(mark) OVER user_marks_window > 0
        THEN ROUND(AVG(mark) OVER () + ((mark - AVG(mark) OVER user_marks_window) / STDDEV_SAMP(mark) OVER user_marks_window) * STDDEV_SAMP(mark) OVER ())
        ELSE mark
      END as mark
    FROM
      kinopoisk_movie_mark
    WINDOW
      user_marks_window AS (PARTITION BY kinopoisk_movie_mark.user_id)
    ), movie_bernoulli_rating AS (
      SELECT
        account.kinopoisk_id as user_id,
        kinopoisk_movie_mark_normalized.movie_id as movie_id,
        CASE
        WHEN
        (
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 9, false)) +
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 10, false)) +
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 7, false)) +
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 8, false))
        ) > 0
        THEN wilson_score_lower_bound(
            (
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 9, false)) +
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 10, false))
            ),
            (
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 7, false)) +
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 8, false))
            ),
            1.96
          ) * wilson_score_lower_bound(
            (
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 7, false)) +
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 8, false)) +
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 9, false)) +
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 10, false))
            ),
            (
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 1, false)) +
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 2, false)) +
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 3, false)) +
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 4, false)) +
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 5, false)) +
              COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 6, false))
            ),
            1.96
          )

        ELSE 0
        END as best_marks_percentage,
        wilson_score_lower_bound(
          (
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 7, false)) +
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 8, false)) +
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 9, false)) +
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 10, false))
          ),
          (
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 1, false)) +
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 2, false)) +
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 3, false)) +
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 4, false)) +
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 5, false)) +
            COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 6, false))
          ),
          1.96
        ) as good_marks_percentage,
        wilson_score_upper_bound((
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 7, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 8, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 9, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 10, false))
        ), (
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 1, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 2, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 3, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 4, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 5, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 6, false))
        ), 1.96) - wilson_score_lower_bound((
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 7, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 8, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 9, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 10, false))
        ), (
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 1, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 2, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 3, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 4, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 5, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 6, false))
        ), 1.96) as interval_width
      FROM
        account
      JOIN
        graphql_friend
        ON graphql_friend.user_id = account.kinopoisk_id
      JOIN
        kinopoisk_movie_mark_normalized
        ON kinopoisk_movie_mark_normalized.user_id = graphql_friend.friend_id
      GROUP BY
        account.kinopoisk_id, kinopoisk_movie_mark_normalized.movie_id
      HAVING
        (
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 7, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 8, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 9, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 10, false))
        ) + (
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 1, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 2, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 3, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 4, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 5, false)) +
          COUNT(NULLIF(kinopoisk_movie_mark_normalized.mark = 6, false))
        ) > 0
    )

  SELECT
    movie_bernoulli_rating.user_id as user_id,
    movie_bernoulli_rating.movie_id as movie_id,
    (ROW_NUMBER() OVER (
      PARTITION BY movie_bernoulli_rating.user_id
      ORDER BY
        movie_bernoulli_rating.best_marks_percentage DESC,
        movie_bernoulli_rating.good_marks_percentage DESC
    ))::integer as "position",
    ROUND(100 * movie_bernoulli_rating.best_marks_percentage) as best_marks_percentage,
    ROUND(100 * movie_bernoulli_rating.good_marks_percentage) as good_marks_percentage
  FROM
    movie_bernoulli_rating
  WHERE
    movie_bernoulli_rating.good_marks_percentage > 0.5
    OR movie_bernoulli_rating.interval_width < 0.4
);

CREATE INDEX graphql_movie_top_pk ON graphql_movie_top (user_id, movie_id);
CREATE INDEX graphql_movie_top_position ON graphql_movie_top (user_id, "position");
CREATE INDEX graphql_movie_top_good_marks_percentage ON graphql_movie_top (user_id, good_marks_percentage);
