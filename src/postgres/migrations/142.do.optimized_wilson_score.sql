-- Optimize Wilson score functions for better performance
-- Replace numeric types with double precision for faster computation

DROP FUNCTION wilson_score_lower_bound(numeric, numeric, numeric);
DROP FUNCTION wilson_score_upper_bound(numeric, numeric, numeric);

-- Optimized Wilson score lower bound function using bigint for counts
-- This provides maximum performance since COUNT() results are integers
-- and bigint operations are faster than floating-point arithmetic
CREATE FUNCTION wilson_score_lower_bound(ns bigint, nf bigint, z double precision)
  RETURNS double precision
  AS $$
    SELECT
      (ns + 0.5 * z * z) / (ns + nf + z * z) -
      z / (ns + nf + z * z) * SQRT(ns * nf / (ns + nf) + 0.25 * z * z)
  $$
  LANGUAGE SQL
  IMMUTABLE
  PARALLEL SAFE;

-- Optimized Wilson score upper bound function using bigint for counts
CREATE FUNCTION wilson_score_upper_bound(ns bigint, nf bigint, z double precision)
  RETURNS double precision
  AS $$
    SELECT
      (ns + 0.5 * z * z) / (ns + nf + z * z) +
      z / (ns + nf + z * z) * SQRT(ns * nf / (ns + nf) + 0.25 * z * z)
  $$
  LANGUAGE SQL
  IMMUTABLE
  PARALLEL SAFE;

-- Additional optimized version with fixed confidence level for most common use case
-- This eliminates the need to pass z=1.96 repeatedly and provides maximum performance
-- Pre-calculated constants: z²=3.8416, 0.5*z²=1.9208, 0.25*z²=0.9604
CREATE FUNCTION wilson_score_lower_bound_95(ns bigint, nf bigint)
  RETURNS double precision
  AS $$
    SELECT
      (ns + 1.9208) / (ns + nf + 3.8416) -
      1.96 / (ns + nf + 3.8416) * SQRT(ns * nf / (ns + nf) + 0.9604)
  $$
  LANGUAGE SQL
  IMMUTABLE
  PARALLEL SAFE;

CREATE FUNCTION wilson_score_upper_bound_95(ns bigint, nf bigint)
  RETURNS double precision
  AS $$
    SELECT
      (ns + 1.9208) / (ns + nf + 3.8416) +
      1.96 / (ns + nf + 3.8416) * SQRT(ns * nf / (ns + nf) + 0.9604)
  $$
  LANGUAGE SQL
  IMMUTABLE
  PARALLEL SAFE;
